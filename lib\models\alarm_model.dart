import 'package:uuid/uuid.dart';

class Alarm {
  final String id;
  final DateTime time;
  final String label;
  final List<bool> repeatDays; // [Sun,Mon,Tue,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,Sat]
  final bool isEnabled;
  final String soundUri;
  final bool vibrate;
  final int snoozeMinutes;
  final bool autoSilence;
  final int autoSilenceMinutes;
  final bool smartWake; // Gradually increase volume

  Alarm({
    String? id,
    required this.time,
    this.label = '',
    List<bool>? repeatDays,
    this.isEnabled = true,
    this.soundUri = 'content://settings/system/alarm_sound',
    this.vibrate = true,
    this.snoozeMinutes = 10,
    this.autoSilence = true,
    this.autoSilenceMinutes = 1,
    this.smartWake = false,
  }) : id = id ?? const Uuid().v4(),
       repeatDays = repeatDays ?? List.filled(7, false);

  bool get isRepeating => repeatDays.any((day) => day);
  
  bool get isOneTime => !isRepeating;

  String get repeatDescription {
    if (!isRepeating) return 'Once';
    
    final days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    final selectedDays = [
      for (var i = 0; i < 7; i++)
        if (repeatDays[i]) days[i]
    ];
    
    if (selectedDays.length == 7) return 'Every day';
    if (selectedDays.length == 5 && 
        repeatDays[1] && repeatDays[2] && repeatDays[3] && 
        repeatDays[4] && repeatDays[5]) {
      return 'Weekdays';
    }
    if (selectedDays.length == 2 && 
        repeatDays[0] && repeatDays[6]) {
      return 'Weekends';
    }
    
    return selectedDays.join(', ');
  }

  DateTime getNextAlarmTime() {
    final now = DateTime.now();
    if (!isRepeating) {
      if (time.isBefore(now)) {
        return DateTime(
          now.year,
          now.month,
          now.day + 1,
          time.hour,
          time.minute,
        );
      }
      return time;
    }

    // Handle repeating alarms
    var nextTime = time;
    while (nextTime.isBefore(now) || 
           !repeatDays[nextTime.weekday % 7]) {
      nextTime = DateTime(
        nextTime.year,
        nextTime.month,
        nextTime.day + 1,
        time.hour,
        time.minute,
      );
    }
    return nextTime;
  }

  Alarm copyWith({
    DateTime? time,
    String? label,
    List<bool>? repeatDays,
    bool? isEnabled,
    String? soundUri,
    bool? vibrate,
    int? snoozeMinutes,
    bool? autoSilence,
    int? autoSilenceMinutes,
    bool? smartWake,
  }) {
    return Alarm(
      id: id,
      time: time ?? this.time,
      label: label ?? this.label,
      repeatDays: repeatDays ?? List.from(this.repeatDays),
      isEnabled: isEnabled ?? this.isEnabled,
      soundUri: soundUri ?? this.soundUri,
      vibrate: vibrate ?? this.vibrate,
      snoozeMinutes: snoozeMinutes ?? this.snoozeMinutes,
      autoSilence: autoSilence ?? this.autoSilence,
      autoSilenceMinutes: autoSilenceMinutes ?? this.autoSilenceMinutes,
      smartWake: smartWake ?? this.smartWake,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'time': time.toIso8601String(),
      'label': label,
      'repeatDays': repeatDays.join(','),
      'isEnabled': isEnabled ? 1 : 0,
      'soundUri': soundUri,
      'vibrate': vibrate ? 1 : 0,
      'snoozeMinutes': snoozeMinutes,
      'autoSilence': autoSilence ? 1 : 0,
      'autoSilenceMinutes': autoSilenceMinutes,
      'smartWake': smartWake ? 1 : 0,
    };
  }

  factory Alarm.fromMap(Map<String, dynamic> map) {
    return Alarm(
      id: map['id'] as String,
      time: DateTime.parse(map['time'] as String),
      label: map['label'] as String,
      repeatDays: (map['repeatDays'] as String)
          .split(',')
          .map((e) => e == 'true')
          .toList(),
      isEnabled: map['isEnabled'] == 1,
      soundUri: map['soundUri'] as String,
      vibrate: map['vibrate'] == 1,
      snoozeMinutes: map['snoozeMinutes'] as int,
      autoSilence: map['autoSilence'] == 1,
      autoSilenceMinutes: map['autoSilenceMinutes'] as int,
      smartWake: map['smartWake'] == 1,
    );
  }
}
