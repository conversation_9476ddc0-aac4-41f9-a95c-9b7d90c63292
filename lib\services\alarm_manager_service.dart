import 'dart:isolate';
import 'package:android_alarm_manager_plus/android_alarm_manager_plus.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/alarm_model.dart';

class AlarmManagerService {
  static final AlarmManagerService _instance = AlarmManagerService._internal();
  factory AlarmManagerService() => _instance;
  AlarmManagerService._internal();

  static Future<void> alarmCallback(int id) async {
    // This is called in an isolate when the alarm is triggered
    final prefs = await SharedPreferences.getInstance();
    
    // Get alarm details from SharedPreferences
    final alarmJson = prefs.getString('alarm_$id');
    if (alarmJson == null) return;

    // Initialize notifications
    final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    const androidSettings = AndroidInitializationSettings('app_icon');
    await flutterLocalNotificationsPlugin.initialize(
      InitializationSettings(android: androidSettings),
    );

    // Show the alarm notification
    const androidDetails = AndroidNotificationDetails(
      'alarm_channel',
      'Alarm Notifications',
      channelDescription: 'Notifications for alarms',
      importance: Importance.max,
      priority: Priority.high,
      fullScreenIntent: true,
    );

    await flutterLocalNotificationsPlugin.show(
      id,
      'Alarm',
      'Time to wake up!',
      const NotificationDetails(android: androidDetails),
    );

    // Reschedule if it's a repeating alarm
    // Implementation will be added for repeat scheduling
  }

  Future<void> scheduleAlarm(AlarmModel alarm) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('alarm_${alarm.id}', alarm.toJson());

    // Calculate the alarm time
    final now = DateTime.now();
    var scheduledTime = DateTime(
      now.year,
      now.month,
      now.day,
      alarm.hour,
      alarm.minute,
    );

    // If the time has already passed today, schedule for tomorrow
    if (scheduledTime.isBefore(now)) {
      scheduledTime = scheduledTime.add(const Duration(days: 1));
    }

    // Schedule the alarm
    await AndroidAlarmManager.oneDayAt(
      alarm.id,
      alarm.hour,
      alarm.minute,
      alarmCallback,
      exact: true,
      wakeup: true,
      rescheduleOnReboot: true,
    );
  }

  Future<void> cancelAlarm(int id) async {
    await AndroidAlarmManager.cancel(id);
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('alarm_$id');
  }
}
