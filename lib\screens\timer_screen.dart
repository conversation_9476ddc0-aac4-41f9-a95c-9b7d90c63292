import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/timer_service.dart';
import '../models/timer_model.dart';

class TimerScreen extends StatefulWidget {
  const TimerScreen({Key? key}) : super(key: key);

  @override
  State<TimerScreen> createState() => _TimerScreenState();
}

class _TimerScreenState extends State<TimerScreen> {
  int _hours = 0;
  int _minutes = 0;
  int _seconds = 0;

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    
    if (duration.inHours > 0) {
      return '$hours:$minutes:$seconds';
    }
    return '$minutes:$seconds';
  }

  Widget _buildTimePickerSpinner() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: ListWheelScrollView(
            itemExtent: 50,
            useMagnifier: true,
            magnification: 1.5,
            physics: const FixedExtentScrollPhysics(),
            onSelectedItemChanged: (index) {
              setState(() => _hours = index);
            },
            children: List.generate(
              24,
              (index) => Center(
                child: Text(
                  '${index}h',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ),
            ),
          ),
        ),
        Expanded(
          child: ListWheelScrollView(
            itemExtent: 50,
            useMagnifier: true,
            magnification: 1.5,
            physics: const FixedExtentScrollPhysics(),
            onSelectedItemChanged: (index) {
              setState(() => _minutes = index);
            },
            children: List.generate(
              60,
              (index) => Center(
                child: Text(
                  '${index}m',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ),
            ),
          ),
        ),
        Expanded(
          child: ListWheelScrollView(
            itemExtent: 50,
            useMagnifier: true,
            magnification: 1.5,
            physics: const FixedExtentScrollPhysics(),
            onSelectedItemChanged: (index) {
              setState(() => _seconds = index);
            },
            children: List.generate(
              60,
              (index) => Center(
                child: Text(
                  '${index}s',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRunningTimer(TimerModel timer) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          _formatDuration(timer.remainingTime),
          style: Theme.of(context).textTheme.displayLarge,
        ),
        const SizedBox(height: 32),
        CircularProgressIndicator(
          value: timer.progress,
          strokeWidth: 8,
        ),
        const SizedBox(height: 32),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            FloatingActionButton(
              heroTag: 'reset',
              onPressed: () => context.read<TimerService>().resetTimer(timer),
              child: const Icon(Icons.refresh),
            ),
            FloatingActionButton.large(
              heroTag: 'startStop',
              onPressed: () {
                if (timer.isRunning) {
                  context.read<TimerService>().pauseTimer(timer);
                } else {
                  context.read<TimerService>().resumeTimer(timer);
                }
              },
              child: Icon(timer.isRunning ? Icons.pause : Icons.play_arrow),
            ),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Timer'),
      ),      body: Consumer<TimerService>(
        builder: (context, service, child) {
          final activeTimer = service.timers.isNotEmpty
              ? service.timers.first
              : null;

          if (activeTimer != null) {
            return _buildRunningTimer(activeTimer);
          }

          return Column(
            children: [
              Expanded(
                child: _buildTimePickerSpinner(),
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: FloatingActionButton.extended(
                  onPressed: () {
                    if (_hours == 0 && _minutes == 0 && _seconds == 0) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Please set a duration'),
                        ),
                      );
                      return;
                    }

                    final duration = Duration(
                      hours: _hours,
                      minutes: _minutes,
                      seconds: _seconds,
                    );
                        context.read<TimerService>().startNewTimer(
                          duration: duration,
                        );
                  },
                  label: const Text('Start Timer'),
                  icon: const Icon(Icons.play_arrow),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
