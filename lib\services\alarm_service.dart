import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:android_alarm_manager_plus/android_alarm_manager_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/alarm_model.dart';
import 'notification_service.dart';

class AlarmService extends ChangeNotifier {
  static final AlarmService _instance = AlarmService._internal();
  factory AlarmService() => _instance;
  AlarmService._internal();

  static const String _alarmsKey = 'alarms';
  final NotificationService _notificationService = NotificationService();
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;
    
    // Initialize Android Alarm Manager
    await AndroidAlarmManager.initialize();

    // Load and reschedule any saved alarms
    final alarms = await _loadAlarms();
    for (final alarm in alarms) {
      if (alarm.isEnabled) {
        await scheduleAlarm(alarm);
      }
    }

    _isInitialized = true;
  }

  Future<void> scheduleAlarm(Alarm alarm) async {
    if (!_isInitialized) {
      throw StateError('AlarmService must be initialized before scheduling alarms');
    }

    if (!alarm.isEnabled) return;

    final now = DateTime.now();
    DateTime scheduledTime = alarm.getNextAlarmTime();

    // If smart wake is enabled, schedule the pre-alarm
    if (alarm.smartWake) {
      final preAlarmTime = scheduledTime.subtract(const Duration(minutes: 30));
      if (preAlarmTime.isAfter(now)) {
        await AndroidAlarmManager.oneShotAt(
          preAlarmTime,
          alarm.hashCode + 1, // Use different ID for pre-alarm
          preAlarmCallback,
          exact: true,
          wakeup: true,
          rescheduleOnReboot: true,
        );
      }
    }

    // Schedule main alarm
    if (alarm.isRepeating) {
      await AndroidAlarmManager.periodic(
        const Duration(days: 1),
        alarm.hashCode,
        alarmCallback,
        exact: true,
        wakeup: true,
        startAt: scheduledTime,
        rescheduleOnReboot: true,
      );
    } else {
      await AndroidAlarmManager.oneShotAt(
        scheduledTime,
        alarm.hashCode,
        alarmCallback,
        exact: true,
        wakeup: true,
        rescheduleOnReboot: true,
      );
    }

    // Save alarm to persistent storage
    await _saveAlarm(alarm);
    notifyListeners();
  }

  Future<void> cancelAlarm(Alarm alarm) async {
    await AndroidAlarmManager.cancel(alarm.hashCode);
    if (alarm.smartWake) {
      await AndroidAlarmManager.cancel(alarm.hashCode + 1);
    }
    await _saveAlarm(alarm.copyWith(isEnabled: false));
    notifyListeners();
  }

  @pragma('vm:entry-point')
  static Future<void> preAlarmCallback(int id) async {
    try {
      final alarmService = AlarmService();
      final alarm = await alarmService._getAlarm(id - 1); // Get original alarm ID
      
      if (alarm != null) {
        final notificationService = NotificationService();
        await notificationService.initialize();

        // Show gentle wake up notification
        await notificationService.showAlarmNotification(
          alarm,
          isPreAlarm: true,
        );
      }
    } catch (e) {
      print('Error in pre-alarm callback: $e');
    }
  }

  @pragma('vm:entry-point')
  static Future<void> alarmCallback(int id) async {
    try {
      final alarmService = AlarmService();
      final alarm = await alarmService._getAlarm(id);
      
      if (alarm != null) {
        final notificationService = NotificationService();
        await notificationService.initialize();
        await notificationService.showAlarmNotification(alarm);

        // Cancel pre-alarm if it's still running
        if (alarm.smartWake) {
          await AndroidAlarmManager.cancel(id + 1);
        }

        // Handle repeating alarms
        if (alarm.isRepeating) {
          await alarmService.scheduleAlarm(alarm);
        } else {
          // Disable one-time alarm after it fires
          final updatedAlarm = alarm.copyWith(isEnabled: false);
          await alarmService._saveAlarm(updatedAlarm);
          alarmService.notifyListeners();
        }
      }
    } catch (e) {
      print('Error in alarm callback: $e');
    }
  }

  Future<void> _saveAlarm(Alarm alarm) async {
    final prefs = await SharedPreferences.getInstance();
    final alarms = await _loadAlarms();
    alarms.removeWhere((a) => a.id == alarm.id);
    alarms.add(alarm);
    
    final alarmsJson = jsonEncode(alarms.map((a) => a.toMap()).toList());
    await prefs.setString(_alarmsKey, alarmsJson);
  }

  Future<List<Alarm>> _loadAlarms() async {
    final prefs = await SharedPreferences.getInstance();
    final String? alarmsJson = prefs.getString(_alarmsKey);
    if (alarmsJson == null) return [];

    try {
      final List<dynamic> alarmsList = jsonDecode(alarmsJson);
      return alarmsList
          .map((data) => Alarm.fromMap(Map<String, dynamic>.from(data)))
          .toList();
    } catch (e) {
      print('Error loading alarms: $e');
      return [];
    }
  }

  Future<Alarm?> _getAlarm(int id) async {
    try {
      final alarms = await _loadAlarms();
      return alarms.firstWhere((alarm) => alarm.hashCode == id);
    } catch (e) {
      print('Error getting alarm: $e');
      return null;
    }
  }
}
