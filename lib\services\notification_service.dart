import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/alarm_model.dart';
import '../models/timer_model.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._();
  factory NotificationService() => _instance;
  NotificationService._();

  static const String alarmChannelId = 'alarms';
  static const String preAlarmChannelId = 'pre_alarms';
  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();

  Future<void> initialize() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const settings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      settings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Request permissions and create channels
    await requestPermissions();
  }

  Future<bool> requestPermissions() async {
    // Request notification permission
    final notificationPermission = await Permission.notification.request();
    if (notificationPermission.isDenied) {
      return false;
    }

    // Request exact alarm permission (Android 12+)
    if (await Permission.scheduleExactAlarm.shouldShowRequestRationale) {
      final alarmPermission = await Permission.scheduleExactAlarm.request();
      if (alarmPermission.isDenied) {
        return false;
      }
    }

    // Initialize notification channels after permissions are granted
    await _createNotificationChannels();
    return true;
  }

  Future<void> _createNotificationChannels() async {
    final plugin = _notifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
    
    if (plugin == null) return;

    // Main alarm channel
    await plugin.createNotificationChannel(
      const AndroidNotificationChannel(
        alarmChannelId,
        'Alarms',
        description: 'Notifications for alarms',
        importance: Importance.max,
        enableVibration: true,
        playSound: true,
        sound: RawResourceAndroidNotificationSound('alarm_sound'),
      ),
    );

    // Pre-alarm channel for smart wake
    await plugin.createNotificationChannel(
      const AndroidNotificationChannel(
        preAlarmChannelId,
        'Smart Wake',
        description: 'Gentle wake up notifications',
        importance: Importance.high,
        enableVibration: true,
        playSound: true,
        sound: RawResourceAndroidNotificationSound('alarm_sound'),
      ),
    );
  }

  void _onNotificationTapped(NotificationResponse response) {
    final payload = response.payload;
    if (payload == null) return;

    if (payload.startsWith('alarm:')) {
      // Handle alarm tap
      final alarmId = payload.substring('alarm:'.length);
      print('Alarm tapped: $alarmId');
    } else if (payload.startsWith('timer:')) {
      // Handle timer tap
      final timerId = payload.substring('timer:'.length);
      print('Timer tapped: $timerId');
    }
  }

  Future<void> showAlarmNotification(
    Alarm alarm, {
    bool isPreAlarm = false,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      isPreAlarm ? preAlarmChannelId : alarmChannelId,
      isPreAlarm ? 'Smart Wake' : 'Alarm',
      channelDescription: isPreAlarm ? 'Gentle wake up notification' : 'Alarm notification',
      importance: isPreAlarm ? Importance.high : Importance.max,
      enableVibration: true,
      playSound: true,
      sound: const RawResourceAndroidNotificationSound('alarm_sound'),
      fullScreenIntent: !isPreAlarm,
      category: AndroidNotificationCategory.alarm,
      audioAttributesUsage: AudioAttributesUsage.alarm,
    );

    final iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      sound: 'alarm_sound.aiff',
      interruptionLevel: InterruptionLevel.timeSensitive,
    );

    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    final title = isPreAlarm ? 'Smart Wake' : 'Alarm';
    final message = alarm.label.isEmpty 
      ? (isPreAlarm ? 'Preparing to wake up' : 'Time to wake up!')
      : alarm.label;

    await _notifications.show(
      isPreAlarm ? alarm.hashCode + 1 : alarm.hashCode,
      title,
      message,
      details,
      payload: 'alarm:${alarm.id}',
    );
  }

  Future<void> showTimerNotification(TimerModel timer) async {
    const androidDetails = AndroidNotificationDetails(
      'timers',
      'Timers',
      channelDescription: 'Notifications for timers',
      importance: Importance.high,
      priority: Priority.high,
      category: AndroidNotificationCategory.alarm,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      timer.hashCode,
      'Timer Complete',
      timer.label.isEmpty ? 'Your timer is done!' : timer.label,
      details,
      payload: 'timer:${timer.id}',
    );
  }

  Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }
}
