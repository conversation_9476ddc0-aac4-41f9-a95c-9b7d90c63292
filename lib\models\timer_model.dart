import 'package:uuid/uuid.dart';

class TimerModel {
  final String id;
  final Duration duration;
  final Duration remainingTime;
  final bool isRunning;
  final String label;
  final bool vibrate;
  final String soundUri;
  final DateTime? startTime;
  final DateTime? pauseTime;

  const TimerModel({
    String? id,
    required this.duration,
    Duration? remainingTime,
    this.isRunning = false,
    this.label = '',
    this.vibrate = true,
    this.soundUri = 'content://settings/system/notification_sound',
    this.startTime,
    this.pauseTime,
  }) : id = id ?? const Uuid().v4(),
       remainingTime = remainingTime ?? duration;

  bool get isCompleted => remainingTime.inSeconds <= 0;
  
  double get progress => 
      1 - (remainingTime.inSeconds / duration.inSeconds);

  TimerModel copyWith({
    Duration? duration,
    Duration? remainingTime,
    bool? isRunning,
    String? label,
    bool? vibrate,
    String? soundUri,
    DateTime? startTime,
    DateTime? pauseTime,
  }) {
    return TimerModel(
      id: id,
      duration: duration ?? this.duration,
      remainingTime: remainingTime ?? this.remainingTime,
      isRunning: isRunning ?? this.isRunning,
      label: label ?? this.label,
      vibrate: vibrate ?? this.vibrate,
      soundUri: soundUri ?? this.soundUri,
      startTime: startTime ?? this.startTime,
      pauseTime: pauseTime ?? this.pauseTime,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'duration': duration.inSeconds,
      'remainingTime': remainingTime.inSeconds,
      'isRunning': isRunning ? 1 : 0,
      'label': label,
      'vibrate': vibrate ? 1 : 0,
      'soundUri': soundUri,
      'startTime': startTime?.toIso8601String(),
      'pauseTime': pauseTime?.toIso8601String(),
    };
  }

  factory TimerModel.fromMap(Map<String, dynamic> map) {
    return TimerModel(
      id: map['id'] as String,
      duration: Duration(seconds: map['duration'] as int),
      remainingTime: Duration(seconds: map['remainingTime'] as int),
      isRunning: map['isRunning'] == 1,
      label: map['label'] as String,
      vibrate: map['vibrate'] == 1,
      soundUri: map['soundUri'] as String,
      startTime: map['startTime'] != null 
          ? DateTime.parse(map['startTime'] as String)
          : null,
      pauseTime: map['pauseTime'] != null
          ? DateTime.parse(map['pauseTime'] as String)
          : null,
    );
  }
}
