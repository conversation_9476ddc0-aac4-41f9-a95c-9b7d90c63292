import 'package:flutter/material.dart';
import 'dart:math';

class AnalogClock extends StatelessWidget {
  final DateTime time;
  final double size;
  final Color? hourHandColor;
  final Color? minuteHandColor;
  final Color? secondHandColor;
  final Color? backgroundColor;
  final Color? borderColor;

  const AnalogClock({
    Key? key,
    required this.time,
    this.size = 200.0,
    this.hourHandColor,
    this.minuteHandColor,
    this.secondHandColor,
    this.backgroundColor,
    this.borderColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor ?? Theme.of(context).cardColor,
        border: Border.all(
          color: borderColor ?? Theme.of(context).dividerColor,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: CustomPaint(
        painter: ClockPain<PERSON>(
          time: time,
          hourHandColor: hourHandColor ?? Theme.of(context).primaryColor,
          minuteHandColor: minuteHandColor ?? Theme.of(context).primaryColor.withOpacity(0.8),
          secondHandColor: secondHandColor ?? Theme.of(context).colorScheme.secondary,
        ),
      ),
    );
  }
}

class ClockPainter extends CustomPainter {
  final DateTime time;
  final Color hourHandColor;
  final Color minuteHandColor;
  final Color secondHandColor;

  ClockPainter({
    required this.time,
    required this.hourHandColor,
    required this.minuteHandColor,
    required this.secondHandColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2;

    // Draw clock face markers
    final markerPaint = Paint()
      ..color = Colors.black87
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    for (var i = 0; i < 12; i++) {
      final angle = (i * 30) * pi / 180;
      final markerLength = i % 3 == 0 ? 15.0 : 10.0;
      final outerPoint = Offset(
        center.dx + (radius - 10) * cos(angle),
        center.dy + (radius - 10) * sin(angle),
      );
      final innerPoint = Offset(
        center.dx + (radius - markerLength - 10) * cos(angle),
        center.dy + (radius - markerLength - 10) * sin(angle),
      );
      canvas.drawLine(innerPoint, outerPoint, markerPaint);
    }

    // Draw hour hand
    final hourAngle = (time.hour % 12 + time.minute / 60) * 30 * pi / 180;
    final hourHandLength = radius * 0.5;
    _drawHand(canvas, center, hourAngle, hourHandLength, hourHandColor, 4);

    // Draw minute hand
    final minuteAngle = time.minute * 6 * pi / 180;
    final minuteHandLength = radius * 0.7;
    _drawHand(canvas, center, minuteAngle, minuteHandLength, minuteHandColor, 3);

    // Draw second hand
    final secondAngle = time.second * 6 * pi / 180;
    final secondHandLength = radius * 0.8;
    _drawHand(canvas, center, secondAngle, secondHandLength, secondHandColor, 1);

    // Draw center dot
    canvas.drawCircle(
      center,
      5,
      Paint()..color = secondHandColor,
    );
  }

  void _drawHand(Canvas canvas, Offset center, double angle, double length,
      Color color, double strokeWidth) {
    final hand = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    final point = Offset(
      center.dx + length * sin(angle),
      center.dy - length * cos(angle),
    );

    canvas.drawLine(center, point, hand);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
