class TimeZone {
  final String name;
  final String region;
  final String abbreviation;
  final int offset;
  final bool isDST;

  TimeZone({
    required this.name,
    required this.region,
    required this.abbreviation,
    required this.offset,
    this.isDST = false,
  });

  String get displayName => '$region - $name';

  DateTime get currentTime {
    final now = DateTime.now().toUtc();
    return now.add(Duration(hours: offset));
  }

  String get formattedOffset {
    final sign = offset >= 0 ? '+' : '-';
    final hours = offset.abs();
    return 'UTC$sign$hours';
  }
}
