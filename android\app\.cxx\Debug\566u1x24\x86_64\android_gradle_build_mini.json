{"buildFiles": ["E:\\MySchool\\Year 4\\Semester 1\\3.Mobile Programming I\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\MySchool\\Year 4\\Semester 1\\3.Mobile Programming I\\test\\new_app\\android\\app\\.cxx\\Debug\\566u1x24\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\MySchool\\Year 4\\Semester 1\\3.Mobile Programming I\\test\\new_app\\android\\app\\.cxx\\Debug\\566u1x24\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}