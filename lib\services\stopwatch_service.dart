import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/stopwatch_model.dart';

class StopwatchService extends ChangeNotifier {
  static final StopwatchService _instance = StopwatchService._internal();
  factory StopwatchService() => _instance;
  StopwatchService._internal();

  StopwatchModel _stopwatch = StopwatchModel();
  StopwatchModel get stopwatch => _stopwatch;

  Timer? _ticker;

  void start() {
    if (!_stopwatch.isRunning) {
      _stopwatch = _stopwatch.copyWith(
        isRunning: true,
        startTime: DateTime.now().subtract(_stopwatch.elapsed),
      );
      _startTicker();
      notifyListeners();
    }
  }

  void stop() {
    if (_stopwatch.isRunning) {
      _stopwatch = _stopwatch.copyWith(
        isRunning: false,
        pauseTime: DateTime.now(),
      );
      _ticker?.cancel();
      notifyListeners();
    }
  }

  void reset() {
    _stopwatch = StopwatchModel();
    _ticker?.cancel();
    notifyListeners();
  }

  void lap() {
    if (_stopwatch.isRunning) {
      final lastLapEndTime = _stopwatch.laps.isEmpty
          ? _stopwatch.startTime!
          : _stopwatch.startTime!.add(_stopwatch.laps.last.total);
            
      final lapElapsed = DateTime.now().difference(lastLapEndTime);
      final totalElapsed = DateTime.now().difference(_stopwatch.startTime!);
      
      final lap = LapTime(
        elapsed: lapElapsed,
        total: totalElapsed,
        lapNumber: _stopwatch.laps.length + 1,
      );
      
      _stopwatch = _stopwatch.copyWith(
        laps: [..._stopwatch.laps, lap],
      );
      notifyListeners();
    }
  }

  void _startTicker() {
    _ticker?.cancel();
    _ticker = Timer.periodic(const Duration(milliseconds: 16), (_) {
      if (_stopwatch.isRunning) {
        _stopwatch = _stopwatch.copyWith(
          elapsed: DateTime.now().difference(_stopwatch.startTime!),
        );
        notifyListeners();
      } else {
        _ticker?.cancel();
      }
    });
  }

  @override
  void dispose() {
    _ticker?.cancel();
    super.dispose();
  }
}
