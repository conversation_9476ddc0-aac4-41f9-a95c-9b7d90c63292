import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/clock_service.dart';
import '../models/alarm_model.dart';
import '../widgets/flip_clock.dart';

class AlarmScreen extends StatefulWidget {
  const AlarmScreen({Key? key}) : super(key: key);

  @override
  State<AlarmScreen> createState() => _AlarmScreenState();
}

class _AlarmScreenState extends State<AlarmScreen> {
  TimeOfDay _selectedTime = TimeOfDay.now();
  final List<bool> _selectedDays = List.filled(7, false);
  final TextEditingController _labelController = TextEditingController();
  bool _smartWake = false; // Add smart wake state

  @override
  void dispose() {
    _labelController.dispose();
    super.dispose();
  }

  void _showAddAlarmBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 16,
            right: 16,
            top: 16,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              FlipClock(
                initialTime: _selectedTime,
                onTimeSelected: (time) {
                  setState(() => _selectedTime = time);
                },
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _labelController,
                decoration: const InputDecoration(
                  labelText: 'Label',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    for (var i = 0; i < 7; i++)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: FilterChip(
                          label: Text(['S', 'M', 'T', 'W', 'T', 'F', 'S'][i]),
                          selected: _selectedDays[i],
                          onSelected: (selected) {
                            setState(() => _selectedDays[i] = selected);
                          },
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              // Add Smart Wake option
              SwitchListTile(
                title: const Text('Smart Wake'),
                subtitle: const Text('Gradually increase volume 30 minutes before alarm'),
                value: _smartWake,
                onChanged: (bool value) {
                  setState(() => _smartWake = value);
                },
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  final now = DateTime.now();
                  final alarmTime = DateTime(
                    now.year,
                    now.month,
                    now.day,
                    _selectedTime.hour,
                    _selectedTime.minute,
                  );
                  
                  final alarm = Alarm(
                    time: alarmTime,
                    label: _labelController.text,
                    repeatDays: List.from(_selectedDays),
                    smartWake: _smartWake, // Add smart wake to alarm
                  );
                  
                  context.read<ClockService>().addAlarm(alarm);
                  Navigator.pop(context);
                },
                child: const Text('Save Alarm'),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  void _showEditAlarmBottomSheet(Alarm alarm) {
    _selectedTime = TimeOfDay(hour: alarm.time.hour, minute: alarm.time.minute);
    _labelController.text = alarm.label;
    _selectedDays.setAll(0, alarm.repeatDays);
    _smartWake = alarm.smartWake;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 16,
            right: 16,
            top: 16,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              FlipClock(
                initialTime: _selectedTime,
                onTimeSelected: (time) {
                  setState(() => _selectedTime = time);
                },
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _labelController,
                decoration: const InputDecoration(
                  labelText: 'Label',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    for (var i = 0; i < 7; i++)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: FilterChip(
                          label: Text(['S', 'M', 'T', 'W', 'T', 'F', 'S'][i]),
                          selected: _selectedDays[i],
                          onSelected: (selected) {
                            setState(() => _selectedDays[i] = selected);
                          },
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              SwitchListTile(
                title: const Text('Smart Wake'),
                subtitle: const Text('Gradually increase volume 30 minutes before alarm'),
                value: _smartWake,
                onChanged: (bool value) {
                  setState(() => _smartWake = value);
                },
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  final now = DateTime.now();
                  final updatedAlarm = alarm.copyWith(
                    time: DateTime(
                      now.year,
                      now.month,
                      now.day,
                      _selectedTime.hour,
                      _selectedTime.minute,
                    ),
                    label: _labelController.text,
                    repeatDays: List.from(_selectedDays),
                    smartWake: _smartWake,
                  );
                  
                  context.read<ClockService>().updateAlarm(alarm, updatedAlarm);
                  Navigator.pop(context);
                },
                child: const Text('Update Alarm'),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Alarms'),
      ),
      body: Consumer<ClockService>(
        builder: (context, clockService, child) {
          final alarms = clockService.alarms;
          return ListView.builder(
            itemCount: alarms.length,
            itemBuilder: (context, index) {
              final alarm = alarms[index];
              return Dismissible(
                key: Key(alarm.id),
                background: Container(
                  color: Colors.red,
                  alignment: Alignment.centerRight,
                  padding: const EdgeInsets.only(right: 16),
                  child: const Icon(Icons.delete, color: Colors.white),
                ),
                direction: DismissDirection.endToStart,
                onDismissed: (_) {
                  clockService.removeAlarm(alarm);
                },
                child: ListTile(
                  leading: Icon(
                    Icons.alarm,
                    color: alarm.smartWake ? Colors.blue : null,
                  ),
                  title: Text(alarm.label.isEmpty ? 'Alarm' : alarm.label),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${alarm.time.hour.toString().padLeft(2, '0')}:'
                        '${alarm.time.minute.toString().padLeft(2, '0')} - '
                        '${alarm.repeatDescription}',
                      ),
                      if (alarm.smartWake)
                        Text(
                          'Smart Wake enabled',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.primary,
                            fontSize: 12,
                          ),
                        ),
                    ],
                  ),
                  trailing: Switch(
                    value: alarm.isEnabled,
                    onChanged: (value) {
                      clockService.toggleAlarm(alarm);
                    },
                  ),
                  // Add onTap handler to ListTile for editing
                  onTap: () => _showEditAlarmBottomSheet(alarm),
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddAlarmBottomSheet,
        child: const Icon(Icons.add),
      ),
    );
  }
}
