import 'package:flutter/material.dart';
import '../models/timezone_model.dart';

class AddTimeZoneBottomSheet extends StatefulWidget {
  final Function(TimeZone) onTimeZoneAdded;

  const AddTimeZoneBottomSheet({
    Key? key,
    required this.onTimeZoneAdded,
  }) : super(key: key);

  @override
  State<AddTimeZoneBottomSheet> createState() => _AddTimeZoneBottomSheetState();
}

class _AddTimeZoneBottomSheetState extends State<AddTimeZoneBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _regionController = TextEditingController();
  final _abbreviationController = TextEditingController();
  int _offset = 0;
  bool _isDST = false;

  @override
  void dispose() {
    _nameController.dispose();
    _regionController.dispose();
    _abbreviationController.dispose();
    super.dispose();
  }

  void _submitForm() {
    if (_formKey.currentState?.validate() ?? false) {
      final newTimeZone = TimeZone(
        name: _nameController.text.trim(),
        region: _regionController.text.trim(),
        abbreviation: _abbreviationController.text.trim().toUpperCase(),
        offset: _offset,
        isDST: _isDST,
      );
      widget.onTimeZoneAdded(newTimeZone);
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 16,
        right: 16,
        top: 16,
      ),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Add New Time Zone',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'City Name',
                hintText: 'e.g., Paris',
              ),
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Please enter a city name';
                }
                return null;
              },
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _regionController,
              decoration: const InputDecoration(
                labelText: 'Region',
                hintText: 'e.g., Europe',
              ),
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Please enter a region';
                }
                return null;
              },
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _abbreviationController,
              decoration: const InputDecoration(
                labelText: 'Time Zone Abbreviation',
                hintText: 'e.g., CET',
              ),
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Please enter a time zone abbreviation';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('UTC Offset:'),
                Expanded(
                  child: Slider(
                    value: _offset.toDouble(),
                    min: -12,
                    max: 14,
                    divisions: 26,
                    label: '${_offset >= 0 ? '+' : ''}$_offset',
                    onChanged: (value) {
                      setState(() {
                        _offset = value.round();
                      });
                    },
                  ),
                ),
                Text('${_offset >= 0 ? '+' : ''}$_offset'),
              ],
            ),
            Row(
              children: [
                const Text('Daylight Saving Time'),
                const Spacer(),
                Switch(
                  value: _isDST,
                  onChanged: (value) {
                    setState(() {
                      _isDST = value;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _submitForm,
              child: const Text('Add Time Zone'),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
