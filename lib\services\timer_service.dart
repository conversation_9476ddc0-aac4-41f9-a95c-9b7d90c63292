import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/timer_model.dart';
import 'notification_service.dart';

class TimerService extends ChangeNotifier {
  static final TimerService _instance = TimerService._internal();
  factory TimerService() => _instance;
  TimerService._internal();

  final List<TimerModel> _timers = [];
  List<TimerModel> get timers => List.unmodifiable(_timers);
  
  Timer? _ticker;
  final NotificationService _notificationService = NotificationService();

  void startNewTimer({
    required Duration duration,
    String label = '',
    bool vibrate = true,
    String? soundUri,
  }) {
    final timer = TimerModel(
      duration: duration,
      label: label,
      vibrate: vibrate,
      soundUri: soundUri ?? 'content://settings/system/notification_sound',
      isRunning: true,
      startTime: DateTime.now(),
    );
    
    _timers.add(timer);
    _startTicker();
    notifyListeners();
  }

  void pauseTimer(TimerModel timer) {
    final index = _timers.indexOf(timer);
    if (index != -1) {
      _timers[index] = timer.copyWith(
        isRunning: false,
        pauseTime: DateTime.now(),
      );
      _checkAndUpdateTicker();
      notifyListeners();
    }
  }

  void resumeTimer(TimerModel timer) {
    final index = _timers.indexOf(timer);
    if (index != -1) {
      final pauseDuration = DateTime.now().difference(timer.pauseTime!);
      _timers[index] = timer.copyWith(
        isRunning: true,
        startTime: timer.startTime!.add(pauseDuration),
        pauseTime: null,
      );
      _startTicker();
      notifyListeners();
    }
  }

  void resetTimer(TimerModel timer) {
    final index = _timers.indexOf(timer);
    if (index != -1) {
      _timers[index] = timer.copyWith(
        remainingTime: timer.duration,
        isRunning: false,
        startTime: null,
        pauseTime: null,
      );
      _checkAndUpdateTicker();
      notifyListeners();
    }
  }

  void removeTimer(TimerModel timer) {
    _timers.remove(timer);
    _checkAndUpdateTicker();
    notifyListeners();
  }

  void _startTicker() {
    _ticker?.cancel();
    _ticker = Timer.periodic(const Duration(milliseconds: 100), (_) {
      bool needsNotify = false;
      bool hasRunningTimer = false;

      for (var i = 0; i < _timers.length; i++) {
        final timer = _timers[i];
        if (timer.isRunning) {
          hasRunningTimer = true;
          final elapsed = DateTime.now().difference(timer.startTime!);
          final remaining = timer.duration - elapsed;

          if (remaining.isNegative) {
            _timers[i] = timer.copyWith(
              remainingTime: Duration.zero,
              isRunning: false,
            );
            _notificationService.showTimerNotification(timer);
            needsNotify = true;
          } else {
            final oldRemaining = _timers[i].remainingTime;
            if (oldRemaining.inSeconds != remaining.inSeconds) {
              _timers[i] = timer.copyWith(remainingTime: remaining);
              needsNotify = true;
            }
          }
        }
      }

      if (!hasRunningTimer) {
        _ticker?.cancel();
      }

      if (needsNotify) {
        notifyListeners();
      }
    });
  }

  void _checkAndUpdateTicker() {
    if (!_timers.any((timer) => timer.isRunning)) {
      _ticker?.cancel();
    }
  }

  @override
  void dispose() {
    _ticker?.cancel();
    super.dispose();
  }
}
