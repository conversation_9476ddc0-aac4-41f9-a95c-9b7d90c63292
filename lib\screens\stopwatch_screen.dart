import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/stopwatch_service.dart';
import '../models/stopwatch_model.dart';

class StopwatchScreen extends StatelessWidget {
  const StopwatchScreen({Key? key}) : super(key: key);

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String threeDigits(int n) => n.toString().padLeft(3, '0');
    
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    final milliseconds = threeDigits(duration.inMilliseconds.remainder(1000));
    
    if (duration.inHours > 0) {
      return '$hours:$minutes:$seconds.$milliseconds';
    }
    return '$minutes:$seconds.$milliseconds';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Stopwatch'),
      ),      body: Consumer<StopwatchService>(
        builder: (context, service, child) {
          final stopwatch = service.stopwatch;
          
          return Column(
            children: [
              Expanded(
                child: Center(
                  child: Text(
                    _formatDuration(stopwatch.elapsed),
                    style: Theme.of(context).textTheme.displayLarge,
                  ),
                ),
              ),
              if (stopwatch.laps.isNotEmpty)
                Expanded(
                  child: ListView.builder(
                    itemCount: stopwatch.laps.length,
                    itemBuilder: (context, index) {
                      final lap = stopwatch.laps[index];
                      final isfastest = lap == stopwatch.fastestLap;
                      final isSlowest = lap == stopwatch.slowestLap;
                      
                      return ListTile(
                        leading: Text(
                          'Lap ${lap.lapNumber}',
                          style: TextStyle(                          color: isfastest
                                ? Colors.green
                                : isSlowest
                                    ? Colors.red
                                    : null,
                          ),
                        ),
                        title: Text(_formatDuration(lap.elapsed)),
                        trailing: Text(_formatDuration(lap.total)),
                      );
                    },
                  ),
                ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    FloatingActionButton(
                      heroTag: 'reset',
                      onPressed: stopwatch.elapsed.inMilliseconds == 0
                          ? null
                          : () => service.reset(),
                      child: const Icon(Icons.refresh),
                    ),
                    FloatingActionButton.large(
                      heroTag: 'startStop',
                      onPressed: () {
                        if (stopwatch.isRunning) {
                          service.stop();
                        } else {
                          service.start();
                        }
                      },
                      child: Icon(
                        stopwatch.isRunning ? Icons.pause : Icons.play_arrow,
                      ),
                    ),
                    FloatingActionButton(
                      heroTag: 'lap',
                      onPressed: !stopwatch.isRunning
                          ? null
                          : () => service.lap(),
                      child: const Icon(Icons.flag),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
