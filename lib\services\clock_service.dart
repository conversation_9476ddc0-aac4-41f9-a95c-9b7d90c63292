import 'dart:async';
import 'package:flutter/material.dart';
import 'package:android_alarm_manager_plus/android_alarm_manager_plus.dart';
import '../models/timezone_model.dart';
import '../models/alarm_model.dart';
import '../models/timer_model.dart';
import '../models/stopwatch_model.dart';
import 'notification_service.dart';

class ClockService extends ChangeNotifier {
  static final ClockService _instance = ClockService._internal();
  factory ClockService() => _instance;
  ClockService._internal() {
    _startTimer();
  }

  final List<TimeZone> _timeZones = [
    TimeZone(
      name: 'London',
      region: 'Europe',
      abbreviation: 'GMT',
      offset: 0,
    ),
    TimeZone(
      name: 'New York',
      region: 'America',
      abbreviation: 'EDT',
      offset: -4,
      isDST: true,
    ),
    TimeZone(
      name: 'Tokyo',
      region: 'Asia',
      abbreviation: 'JST',
      offset: 9,
    ),
    TimeZone(
      name: 'Sydney',
      region: 'Australia',
      abbreviation: 'AEST',
      offset: 10,
    ),
  ];

  final List<Alarm> _alarms = [];
  List<Alarm> get alarms => List.unmodifiable(_alarms);

  // Timers
  final List<TimerModel> _timers = [];
  List<TimerModel> get timers => List.unmodifiable(_timers);

  // Stopwatch
  StopwatchModel _stopwatch = const StopwatchModel();
  StopwatchModel get stopwatch => _stopwatch;

  Timer? _timer;
  DateTime _currentTime = DateTime.now();

  ClockService() {
    _startTimer();
  }

  List<TimeZone> get timeZones => _timeZones;
  DateTime get currentTime => _currentTime;

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _currentTime = DateTime.now();
      notifyListeners();
    });
  }

  void addTimeZone(TimeZone timeZone) {
    _timeZones.add(timeZone);
    notifyListeners();
  }

  void removeTimeZone(TimeZone timeZone) {
    _timeZones.remove(timeZone);
    notifyListeners();
  }

  // Alarm methods
  void updateAlarm(Alarm oldAlarm, Alarm newAlarm) async {
    final index = _alarms.indexOf(oldAlarm);
    if (index != -1) {
      await _cancelAlarm(oldAlarm);
      _alarms[index] = newAlarm;
      await _scheduleAlarm(newAlarm);
      notifyListeners();
    }
  }

  void addAlarm(Alarm alarm) async {
    _alarms.add(alarm);
    await _scheduleAlarm(alarm);
    notifyListeners();
  }

  void removeAlarm(Alarm alarm) async {
    _alarms.remove(alarm);
    await _cancelAlarm(alarm);
    notifyListeners();
  }

  void toggleAlarm(Alarm alarm) async {
    final index = _alarms.indexOf(alarm);
    if (index != -1) {
      final updatedAlarm = alarm.copyWith(isEnabled: !alarm.isEnabled);
      _alarms[index] = updatedAlarm;
      if (updatedAlarm.isEnabled) {
        await _scheduleAlarm(updatedAlarm);
      } else {
        await _cancelAlarm(updatedAlarm);
      }
      notifyListeners();
    }
  }

  Future<void> _scheduleAlarm(Alarm alarm) async {
    if (!alarm.isEnabled) return;

    final now = DateTime.now();
    DateTime scheduledTime = alarm.getNextAlarmTime();

    // If smart wake is enabled, schedule the pre-alarm
    if (alarm.smartWake) {
      final preAlarmTime = scheduledTime.subtract(const Duration(minutes: 30));
      if (preAlarmTime.isAfter(now)) {
        await AndroidAlarmManager.periodic(
          const Duration(days: 1),
          alarm.hashCode + 1, // Use different ID for pre-alarm
          preAlarmCallback,
          exact: true,
          wakeup: true,
          startAt: preAlarmTime,
          rescheduleOnReboot: true,
        );
      }
    }

    // Schedule main alarm
    if (alarm.isRepeating) {
      await AndroidAlarmManager.periodic(
        const Duration(days: 1),
        alarm.hashCode,
        alarmCallback,
        exact: true,
        wakeup: true,
        startAt: scheduledTime,
        rescheduleOnReboot: true,
      );
    } else {
      await AndroidAlarmManager.oneShot(
        scheduledTime.difference(now),
        alarm.hashCode,
        alarmCallback,
        exact: true,
        wakeup: true,
        rescheduleOnReboot: true,
      );
    }
  }

  Future<void> _cancelAlarm(Alarm alarm) async {
    await AndroidAlarmManager.cancel(alarm.hashCode);
    if (alarm.smartWake) {
      await AndroidAlarmManager.cancel(alarm.hashCode + 1);
    }
  }

  @pragma('vm:entry-point')
  static Future<void> alarmCallback(int id) async {
    final instance = ClockService();
    final notificationService = NotificationService();
    await notificationService.initialize();
    
    final alarm = instance._alarms.firstWhere(
      (a) => a.hashCode == id,
      orElse: () => instance._alarms.first, // Fallback to first alarm if not found
    );

    await notificationService.showAlarmNotification(alarm);

    if (alarm.isRepeating) {
      await instance._scheduleAlarm(alarm);
    } else {
      // Disable one-time alarm after it fires
      instance.updateAlarm(alarm, alarm.copyWith(isEnabled: false));
    }
  }

  @pragma('vm:entry-point')
  static Future<void> preAlarmCallback(int id) async {
    final instance = ClockService();
    final notificationService = NotificationService();
    await notificationService.initialize();
    
    final alarm = instance._alarms.firstWhere(
      (a) => a.hashCode == (id - 1), // Get original alarm ID
      orElse: () => instance._alarms.first, // Fallback to first alarm if not found
    );

    await notificationService.showAlarmNotification(
      alarm,
      isPreAlarm: true,
    );
  }

  // Timer methods
  void startNewTimer({
    required Duration duration,
    String label = '',
  }) {
    final timer = TimerModel(
      duration: duration,
      label: label,
      isRunning: true,
      startTime: DateTime.now(),
    );
    _timers.add(timer);
    _startTimerTick();
    notifyListeners();
  }

  void pauseTimer(TimerModel timer) {
    final index = _timers.indexOf(timer);
    if (index != -1) {
      _timers[index] = timer.copyWith(
        isRunning: false,
        pauseTime: DateTime.now(),
      );
      notifyListeners();
    }
  }

  void resumeTimer(TimerModel timer) {
    final index = _timers.indexOf(timer);
    if (index != -1) {
      _timers[index] = timer.copyWith(
        isRunning: true,
        startTime: DateTime.now(),
      );
      _startTimerTick();
      notifyListeners();
    }
  }

  void resetTimer(TimerModel timer) {
    final index = _timers.indexOf(timer);
    if (index != -1) {
      _timers[index] = timer.copyWith(
        remainingTime: timer.duration,
        isRunning: false,
        startTime: null,
        pauseTime: null,
      );
      notifyListeners();
    }
  }

  void _startTimerTick() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      bool hasRunningTimer = false;
      
      for (var i = 0; i < _timers.length; i++) {
        final timer = _timers[i];
        if (timer.isRunning) {
          hasRunningTimer = true;
          final elapsed = DateTime.now().difference(timer.startTime!);
          final remaining = timer.duration - elapsed;
          
          if (remaining.isNegative) {
            _timers[i] = timer.copyWith(
              remainingTime: Duration.zero,
              isRunning: false,
            );
            // TODO: Show notification when timer completes
          } else {
            _timers[i] = timer.copyWith(remainingTime: remaining);
          }
        }
      }
      
      if (!hasRunningTimer) {
        _timer?.cancel();
      }
      
      notifyListeners();
    });
  }

  // Stopwatch methods
  void startStopwatch() {
    if (!_stopwatch.isRunning) {
      _stopwatch = _stopwatch.copyWith(
        isRunning: true,
        startTime: DateTime.now().subtract(
          _stopwatch.elapsed,
        ),
      );
      _startStopwatchTick();
      notifyListeners();
    }
  }

  void stopStopwatch() {
    if (_stopwatch.isRunning) {
      _stopwatch = _stopwatch.copyWith(
        isRunning: false,
        pauseTime: DateTime.now(),
      );
      notifyListeners();
    }
  }

  void resetStopwatch() {
    _stopwatch = StopwatchModel();
    notifyListeners();
  }

  void lapStopwatch() {
    if (_stopwatch.isRunning) {
      final lastLapEndTime = _stopwatch.laps.isEmpty
          ? _stopwatch.startTime!
          : _stopwatch.startTime!.add(
              _stopwatch.laps.last.total,
            );
            
      final lapElapsed = DateTime.now().difference(lastLapEndTime);
      final totalElapsed = DateTime.now().difference(_stopwatch.startTime!);
      
      final lap = LapTime(
        elapsed: lapElapsed,
        total: totalElapsed,
        lapNumber: _stopwatch.laps.length + 1,
      );
      
      _stopwatch = _stopwatch.copyWith(
        laps: [..._stopwatch.laps, lap],
      );
      notifyListeners();
    }
  }

  void _startStopwatchTick() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (_stopwatch.isRunning) {
        _stopwatch = _stopwatch.copyWith(
          elapsed: DateTime.now().difference(_stopwatch.startTime!),
        );
        notifyListeners();
      } else {
        _timer?.cancel();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
