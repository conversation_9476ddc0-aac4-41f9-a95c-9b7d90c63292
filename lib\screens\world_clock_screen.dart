import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../services/clock_service.dart';
import '../widgets/analog_clock.dart';
import '../widgets/add_timezone_bottom_sheet.dart';

class WorldClockScreen extends StatefulWidget {
  const WorldClockScreen({Key? key}) : super(key: key);

  @override
  State<WorldClockScreen> createState() => _WorldClockScreenState();
}

class _WorldClockScreenState extends State<WorldClockScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<ClockService>(
      builder: (context, clockService, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('World Clock'),
            elevation: 0,
            backgroundColor: Theme.of(context).primaryColor,
          ),
          body: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: clockService.timeZones.length,
            itemBuilder: (context, index) {
              final timeZone = clockService.timeZones[index];
              final localTime = timeZone.currentTime;
              
              return Card(
                elevation: 4,
                margin: const EdgeInsets.only(bottom: 16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 120,
                        height: 120,
                        child: AnalogClock(
                          time: localTime,
                          size: 120,
                          backgroundColor: Theme.of(context).cardColor,
                          borderColor: Theme.of(context).primaryColor,
                          hourHandColor: Theme.of(context).primaryColor,
                          minuteHandColor: Theme.of(context).primaryColor.withOpacity(0.8),
                          secondHandColor: Theme.of(context).colorScheme.secondary,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              timeZone.displayName,
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              DateFormat('HH:mm:ss').format(localTime),
                              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${timeZone.formattedOffset} ${timeZone.abbreviation}',
                              style: Theme.of(context).textTheme.bodyLarge,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),          floatingActionButton: FloatingActionButton(
            onPressed: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                ),
                builder: (context) => AddTimeZoneBottomSheet(
                  onTimeZoneAdded: (timeZone) {
                    clockService.addTimeZone(timeZone);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Added ${timeZone.displayName}'),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                ),
              );
            },
            child: const Icon(Icons.add),
          ),
        );
      },
    );
  }
}
