import 'package:uuid/uuid.dart';

class LapTime {
  final String id;
  final Duration elapsed;
  final Duration total;
  final int lapNumber;

  const LapTime({
    String? id,
    required this.elapsed,
    required this.total,
    required this.lapNumber,
  }) : id = id ?? const Uuid().v4();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'elapsed': elapsed.inMicroseconds,
      'total': total.inMicroseconds,
      'lapNumber': lapNumber,
    };
  }

  factory LapTime.fromMap(Map<String, dynamic> map) {
    return LapTime(
      id: map['id'] as String,
      elapsed: Duration(microseconds: map['elapsed'] as int),
      total: Duration(microseconds: map['total'] as int),
      lapNumber: map['lapNumber'] as int,
    );
  }
}

class StopwatchModel {
  final String id;
  final Duration elapsed;
  final bool isRunning;
  final List<LapTime> laps;
  final DateTime? startTime;
  final DateTime? pauseTime;

  const StopwatchModel({
    String? id,
    this.elapsed = Duration.zero,
    this.isRunning = false,
    List<LapTime>? laps,
    this.startTime,
    this.pauseTime,
  }) : id = id ?? const Uuid().v4(),
       laps = laps ?? const [];

  LapTime? get fastestLap {
    if (laps.length < 2) return null;
    return laps.reduce((a, b) => 
        a.elapsed < b.elapsed ? a : b);
  }

  LapTime? get slowestLap {
    if (laps.length < 2) return null;
    return laps.reduce((a, b) => 
        a.elapsed > b.elapsed ? a : b);
  }

  StopwatchModel copyWith({
    Duration? elapsed,
    bool? isRunning,
    List<LapTime>? laps,
    DateTime? startTime,
    DateTime? pauseTime,
  }) {
    return StopwatchModel(
      id: id,
      elapsed: elapsed ?? this.elapsed,
      isRunning: isRunning ?? this.isRunning,
      laps: laps ?? List.from(this.laps),
      startTime: startTime ?? this.startTime,
      pauseTime: pauseTime ?? this.pauseTime,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'elapsed': elapsed.inMicroseconds,
      'isRunning': isRunning ? 1 : 0,
      'laps': laps.map((lap) => lap.toMap()).toList(),
      'startTime': startTime?.toIso8601String(),
      'pauseTime': pauseTime?.toIso8601String(),
    };
  }

  factory StopwatchModel.fromMap(Map<String, dynamic> map) {
    return StopwatchModel(
      id: map['id'] as String,
      elapsed: Duration(microseconds: map['elapsed'] as int),
      isRunning: map['isRunning'] == 1,
      laps: (map['laps'] as List)
          .map((lap) => LapTime.fromMap(lap as Map<String, dynamic>))
          .toList(),
      startTime: map['startTime'] != null 
          ? DateTime.parse(map['startTime'] as String)
          : null,
      pauseTime: map['pauseTime'] != null
          ? DateTime.parse(map['pauseTime'] as String)
          : null,
    );
  }
}
