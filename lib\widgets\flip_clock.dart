import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class Flip<PERSON>lock extends StatefulWidget {
  final TimeOfDay initialTime;
  final Function(TimeOfDay) onTimeSelected;

  const FlipClock({
    Key? key,
    required this.initialTime,
    required this.onTimeSelected,
  }) : super(key: key);

  @override
  State<FlipClock> createState() => _FlipClockState();
}

class _FlipClockState extends State<FlipClock> {
  late TimeOfDay _currentTime;
  bool _is24HourFormat = true;

  @override
  void initState() {
    super.initState();
    _currentTime = widget.initialTime;
  }

  Widget _buildNumberColumn({
    required int value,
    required int maxValue,
    required Function(int) onChanged,
    required bool showLeadingZero,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.keyboard_arrow_up),
          onPressed: () {
            final newValue = (value + 1) % maxValue;
            onChanged(newValue);
          },
        ),
        Container(
          width: 60,
          height: 80,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              showLeadingZero
                  ? value.toString().padLeft(2, '0')
                  : value.toString(),
              style: Theme.of(context).textTheme.displayMedium,
            ),
          ).animate(
            onComplete: (controller) => controller.repeat(),
          ).flipV(
            duration: const Duration(milliseconds: 300),
          ),
        ),
        IconButton(
          icon: const Icon(Icons.keyboard_arrow_down),
          onPressed: () {
            final newValue = value > 0 ? value - 1 : maxValue - 1;
            onChanged(newValue);
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildNumberColumn(
              value: _is24HourFormat ? _currentTime.hour : _currentTime.hourOfPeriod,
              maxValue: _is24HourFormat ? 24 : 12,
              onChanged: (newHour) {
                final adjustedHour = _is24HourFormat
                    ? newHour
                    : _currentTime.period == DayPeriod.pm
                        ? newHour + 12
                        : newHour;
                setState(() {
                  _currentTime = TimeOfDay(
                    hour: adjustedHour,
                    minute: _currentTime.minute,
                  );
                });
                widget.onTimeSelected(_currentTime);
              },
              showLeadingZero: true,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                ':',
                style: Theme.of(context).textTheme.displayMedium,
              ),
            ),
            _buildNumberColumn(
              value: _currentTime.minute,
              maxValue: 60,
              onChanged: (newMinute) {
                setState(() {
                  _currentTime = TimeOfDay(
                    hour: _currentTime.hour,
                    minute: newMinute,
                  );
                });
                widget.onTimeSelected(_currentTime);
              },
              showLeadingZero: true,
            ),
            if (!_is24HourFormat) ...[
              const SizedBox(width: 16),
              ToggleButtons(
                isSelected: [
                  _currentTime.period == DayPeriod.am,
                  _currentTime.period == DayPeriod.pm,
                ],
                onPressed: (index) {
                  setState(() {
                    final newHour = _currentTime.hour >= 12
                        ? _currentTime.hour - 12
                        : _currentTime.hour + 12;
                    _currentTime = TimeOfDay(
                      hour: newHour,
                      minute: _currentTime.minute,
                    );
                  });
                  widget.onTimeSelected(_currentTime);
                },
                children: const [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8),
                    child: Text('AM'),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8),
                    child: Text('PM'),
                  ),
                ],
              ),
            ],
          ],
        ),
        const SizedBox(height: 16),
        TextButton.icon(
          icon: Icon(_is24HourFormat ? Icons.watch_later : Icons.schedule),
          label: Text(_is24HourFormat ? '24-hour' : '12-hour'),
          onPressed: () {
            setState(() {
              _is24HourFormat = !_is24HourFormat;
            });
          },
        ),
      ],
    );
  }
}
